
import React from 'react';
import { CheckSquare, Clock, AlertCircle, Plus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';

interface Task {
  id: string;
  title: string;
  priority: 'high' | 'medium' | 'low';
  dueDate: string;
  completed: boolean;
}

const TaskWidget: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  
  const tasks: Task[] = [
    {
      id: '1',
      title: isRTL ? 'مراجعة التقارير الشهرية' : 'Review monthly reports',
      priority: 'high',
      dueDate: '2024-01-15',
      completed: false,
    },
    {
      id: '2',
      title: isRTL ? 'اجتماع فريق التطوير' : 'Development team meeting',
      priority: 'medium',
      dueDate: '2024-01-16',
      completed: false,
    },
    {
      id: '3',
      title: isRTL ? 'إنهاء تصميم الواجهة' : 'Complete UI design',
      priority: 'high',
      dueDate: '2024-01-14',
      completed: true,
    },
    {
      id: '4',
      title: isRTL ? 'كتابة الوثائق التقنية' : 'Write technical documentation',
      priority: 'low',
      dueDate: '2024-01-18',
      completed: false,
    },
  ];

  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'high': return priority.high.className;
      case 'medium': return priority.medium.className;
      case 'low': return priority.low.className;
      default: return 'text-muted-foreground bg-muted';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return AlertCircle;
      case 'medium': return Clock;
      case 'low': return CheckSquare;
      default: return CheckSquare;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 transition-colors duration-200">
      <div className={cn(
        "flex items-center justify-between mb-6",
        isRTL && "flex-row-reverse"
      )}>
        <h2 className="text-xl font-bold">{t('dashboard.todayTasks')}</h2>
        <button className="flex items-center gap-2 px-3 py-1.5 text-sm bg-zenith-gradient text-white rounded-lg hover:shadow-zenith transition-all duration-200">
          <Plus className="w-4 h-4" />
          {t('tasks.newTask')}
        </button>
      </div>

      <div className="space-y-3">
        {tasks.map((task) => {
          const PriorityIcon = getPriorityIcon(task.priority);
          return (
            <div
              key={task.id}
              className={cn(
                "flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 group hover:shadow-sm",
                task.completed 
                  ? "bg-muted/30 border-muted" 
                  : "bg-background border-border hover:border-blue-200",
                isRTL && "flex-row-reverse"
              )}
            >
              <button
                className={cn(
                  "flex-shrink-0 w-5 h-5 rounded border-2 transition-colors duration-200",
                  task.completed
                    ? "bg-blue-500 border-blue-500 text-white"
                    : "border-muted-foreground hover:border-blue-400"
                )}
              >
                {task.completed && <CheckSquare className="w-3 h-3" />}
              </button>

              <div className={cn("flex-1 min-w-0", isRTL && "text-right")}>
                <p className={cn(
                  "font-medium truncate",
                  task.completed ? "line-through text-muted-foreground" : "text-foreground"
                )}>
                  {task.title}
                </p>
                <div className={cn(
                  "flex items-center gap-2 mt-1 text-xs text-muted-foreground",
                  isRTL && "flex-row-reverse"
                )}>
                  <Clock className="w-3 h-3" />
                  <span>{task.dueDate}</span>
                </div>
              </div>

              <div className={cn(
                "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                getPriorityColor(task.priority)
              )}>
                <PriorityIcon className="w-3 h-3" />
                <span>{t(`tasks.priority.${task.priority}`)}</span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-4 pt-4 border-t border-border">
        <div className={cn(
          "flex items-center justify-between text-sm text-muted-foreground",
          isRTL && "flex-row-reverse"
        )}>
          <span>{isRTL ? '3 من 4 مهام مكتملة' : '3 of 4 tasks completed'}</span>
          <span>75%</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2 mt-2">
          <div className="w-3/4 bg-zenith-sage-500 h-full rounded-full transition-all duration-500"></div>
        </div>
      </div>
    </div>
  );
};

export default TaskWidget;
