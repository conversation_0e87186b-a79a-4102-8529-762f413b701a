import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Folder, Calendar } from 'lucide-react';

const ProjectsList: React.FC = () => {
  const { t } = useLanguage();

  // Placeholder data - replace with actual data from your service
  const projects = [
    {
      id: '1',
      name: 'Website Redesign',
      description: 'Complete redesign of company website',
      tasksCount: 12,
      completedTasks: 8,
      dueDate: '2024-07-15',
      status: 'active'
    },
    {
      id: '2',
      name: 'Mobile App Development',
      description: 'Develop mobile application for iOS and Android',
      tasksCount: 25,
      completedTasks: 15,
      dueDate: '2024-08-30',
      status: 'active'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">{t('projects.allProjects')}</h2>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          {t('projects.newProject')}
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {projects.map((project) => (
          <Card key={project.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  <Folder className="w-5 h-5 text-primary" />
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground line-clamp-2">
                {project.description}
              </p>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">
                  {t('projects.tasks')}: {project.completedTasks}/{project.tasksCount}
                </span>
                <div className="flex items-center text-muted-foreground">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(project.dueDate).toLocaleDateString()}
                </div>
              </div>

              <div className="w-full bg-secondary rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all"
                  style={{ 
                    width: `${(project.completedTasks / project.tasksCount) * 100}%` 
                  }}
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {projects.length === 0 && (
        <div className="text-center py-12">
          <Folder className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">{t('projects.noProjects')}</h3>
          <p className="text-muted-foreground mb-4">{t('projects.noProjectsDescription')}</p>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            {t('projects.createFirst')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default ProjectsList;
