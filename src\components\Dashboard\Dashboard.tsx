
import React from 'react';
import { CheckSquare, Calendar, FileText, TrendingUp } from 'lucide-react';
import WelcomeCard from './WelcomeCard';
import StatsCard from './StatsCard';
import TaskWidget from './TaskWidget';
import ProductivityChart from './ProductivityChart';
import QuickActions from './QuickActions';

const Dashboard: React.FC = () => {
  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto bg-background min-h-screen transition-colors duration-200">
      {/* Welcome Section */}
      <div className="grid grid-cols-1 gap-6">
        <WelcomeCard />
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="المهام المكتملة"
          value="24"
          subtitle="اليوم"
          icon={CheckSquare}
          color="primary"
          progress={80}
          trend="up"
          trendValue="15%"
        />
        <StatsCard
          title="المشاريع النشطة"
          value="7"
          subtitle="قيد التنفيذ"
          icon={Calendar}
          color="secondary"
          progress={65}
          trend="up"
          trendValue="2"
        />
        <StatsCard
          title="الملاحظات المنشأة"
          value="156"
          subtitle="هذا الشهر"
          icon={FileText}
          color="success"
          trend="up"
          trendValue="23%"
        />
        <StatsCard
          title="نقاط الإنتاجية"
          value="87%"
          subtitle="المتوسط الأسبوعي"
          icon={TrendingUp}
          color="neutral"
          progress={87}
          trend="up"
          trendValue="5%"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Tasks */}
        <div className="lg:col-span-2 space-y-6">
          <TaskWidget />
          <ProductivityChart />
        </div>

        {/* Right Column - Quick Actions */}
        <div className="space-y-6">
          <QuickActions />
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
