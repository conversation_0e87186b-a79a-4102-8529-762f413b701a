
import React, { useState } from 'react';
import { MessageCircle, Send, X, Lightbulb, Plus, BarChart3, Calendar } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const AIAssistant: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isThinking, setIsThinking] = useState(false);

  const suggestions = [
    { icon: Plus, text: t('ai.createTask'), action: 'create_task' },
    { icon: BarChart3, text: t('ai.analyzeProductivity'), action: 'analyze_productivity' },
    { icon: Calendar, text: t('ai.organizeDay'), action: 'organize_day' },
  ];

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: inputText,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    setIsThinking(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: isRTL 
          ? `شكراً لسؤالك! أنا هنا لمساعدتك في إدارة مهامك ومشاريعك. يمكنني مساعدتك في: إنشاء المهام، تحليل الإنتاجية، تنظيم اليوم، والمزيد.`
          : `Thank you for your question! I'm here to help you manage your tasks and projects. I can assist you with: creating tasks, analyzing productivity, organizing your day, and more.`,
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsThinking(false);
    }, 2000);
  };

  const handleSuggestionClick = (action: string) => {
    const suggestionTexts = {
      create_task: isRTL ? 'أنشئ مهمة جديدة' : 'Create a new task',
      analyze_productivity: isRTL ? 'حلل إنتاجيتي' : 'Analyze my productivity',
      organize_day: isRTL ? 'نظم يومي' : 'Organize my day',
    };
    
    setInputText(suggestionTexts[action as keyof typeof suggestionTexts] || '');
  };

  return (
    <>
      {/* AI Assistant Button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-zenith-gradient text-white rounded-full shadow-zenith-lg hover:shadow-zenith-lg hover:scale-110 transition-all duration-200 flex items-center justify-center zenith-glow"
        title={t('ai.title')}
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      {/* AI Assistant Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-end justify-end p-6">
          <div className="w-96 h-[600px] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-2xl flex flex-col animate-scale-in transition-colors duration-200">
            {/* Header */}
            <div className={cn(
              "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-zenith-gradient text-white rounded-t-2xl",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn("flex items-center gap-3", isRTL && "flex-row-reverse")}>
                <MessageCircle className="w-5 h-5" />
                <h3 className="font-semibold">{t('ai.title')}</h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-white/20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <Lightbulb className="w-12 h-12 mx-auto mb-4 text-zenith-sage-400" />
                  <p className="mb-4">{t('ai.suggestions')}</p>
                  <div className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionClick(suggestion.action)}
                        className={cn(
                          "w-full flex items-center gap-3 p-3 rounded-lg bg-muted hover:bg-muted/80 transition-colors text-left",
                          isRTL && "flex-row-reverse text-right"
                        )}
                      >
                        <suggestion.icon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        <span className="text-sm">{suggestion.text}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex gap-3",
                    message.isUser && (isRTL ? "flex-row-reverse" : "flex-row-reverse")
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[80%] p-3 rounded-2xl",
                      message.isUser
                        ? "bg-zenith-gradient text-white ml-auto"
                        : "bg-accent text-foreground mr-auto"
                    )}
                  >
                    <p className="text-sm leading-relaxed">{message.text}</p>
                  </div>
                </div>
              ))}

              {isThinking && (
                <div className={cn("flex gap-3", isRTL && "flex-row-reverse")}>
                  <div className="bg-accent text-foreground p-3 rounded-2xl mr-auto">
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-zenith-sage-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-sm text-muted-foreground">{t('ai.thinking')}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div className="p-4 border-t border-border">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder={t('ai.placeholder')}
                  className={cn(
                    "flex-1 bg-accent border border-border rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                    isRTL && "text-right"
                  )}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || isThinking}
                  className="p-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AIAssistant;
