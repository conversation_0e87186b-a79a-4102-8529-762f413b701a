
import React, { useState, useEffect } from 'react';
import { Play, Pause, Square, X, Volume2, VolumeX } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

interface FocusModeProps {
  onClose: () => void;
}

const FocusMode: React.FC<FocusModeProps> = ({ onClose }) => {
  const { t, isRTL } = useLanguage();
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isActive, setIsActive] = useState(false);
  const [sessionType, setSessionType] = useState<'work' | 'shortBreak' | 'longBreak'>('work');
  const [completedSessions, setCompletedSessions] = useState(0);
  const [selectedSound, setSelectedSound] = useState<string | null>(null);
  const [soundEnabled, setSoundEnabled] = useState(false);

  const sessionDurations = {
    work: 25 * 60,
    shortBreak: 5 * 60,
    longBreak: 15 * 60,
  };

  const ambientSounds = [
    { id: 'rain', name: t('focus.rain'), emoji: '🌧️' },
    { id: 'ocean', name: t('focus.ocean'), emoji: '🌊' },
    { id: 'forest', name: t('focus.forest'), emoji: '🌲' },
    { id: 'cafe', name: t('focus.cafe'), emoji: '☕' },
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((time) => time - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsActive(false);
      handleSessionComplete();
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, timeLeft]);

  const handleSessionComplete = () => {
    if (sessionType === 'work') {
      setCompletedSessions(prev => prev + 1);
      const nextSession = completedSessions % 4 === 3 ? 'longBreak' : 'shortBreak';
      setSessionType(nextSession);
      setTimeLeft(sessionDurations[nextSession]);
    } else {
      setSessionType('work');
      setTimeLeft(sessionDurations.work);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const toggleTimer = () => {
    setIsActive(!isActive);
  };

  const resetTimer = () => {
    setIsActive(false);
    setTimeLeft(sessionDurations[sessionType]);
  };

  const switchSession = (type: 'work' | 'shortBreak' | 'longBreak') => {
    setSessionType(type);
    setTimeLeft(sessionDurations[type]);
    setIsActive(false);
  };

  const progress = ((sessionDurations[sessionType] - timeLeft) / sessionDurations[sessionType]) * 100;

  const getSessionTitle = () => {
    switch (sessionType) {
      case 'work': return t('focus.workSession');
      case 'shortBreak': return t('focus.shortBreak');
      case 'longBreak': return t('focus.longBreak');
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-3xl p-8 w-full max-w-md shadow-2xl animate-scale-in transition-colors duration-200">
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between mb-8",
          isRTL && "flex-row-reverse"
        )}>
          <h2 className="text-2xl font-bold zenith-gradient-text">{t('focus.title')}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Session Type Selector */}
        <div className="flex gap-2 mb-8 p-1 bg-accent rounded-lg">
          {(['work', 'shortBreak', 'longBreak'] as const).map((type) => (
            <button
              key={type}
              onClick={() => switchSession(type)}
              className={cn(
                "flex-1 py-2 px-3 rounded-md text-sm font-medium transition-all duration-200",
                sessionType === type
                  ? "bg-zenith-gradient text-white shadow-zenith"
                  : "text-muted-foreground hover:text-foreground"
              )}
            >
              {type === 'work' && t('focus.workSession')}
              {type === 'shortBreak' && t('focus.shortBreak')}
              {type === 'longBreak' && t('focus.longBreak')}
            </button>
          ))}
        </div>

        {/* Timer Display */}
        <div className="text-center mb-8">
          <div className="relative w-48 h-48 mx-auto mb-6">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                className="text-muted-foreground/20"
              />
              <circle
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
                strokeDasharray={`${2 * Math.PI * 45}`}
                strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
                className="text-sky-500 transition-all duration-1000 ease-linear"
                strokeLinecap="round"
              />
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-4xl font-bold font-mono mb-2">{formatTime(timeLeft)}</div>
              <div className="text-sm text-muted-foreground">{getSessionTitle()}</div>
            </div>
          </div>

          {/* Session Counter */}
          <div className="flex justify-center gap-2 mb-6">
            {[...Array(4)].map((_, i) => (
              <div
                key={i}
                className={cn(
                  "w-3 h-3 rounded-full",
                  i < completedSessions ? "bg-sky-500" : "bg-muted-foreground/20"
                )}
              />
            ))}
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center gap-4 mb-8">
          <button
            onClick={toggleTimer}
            className="flex items-center gap-2 px-6 py-3 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200 font-medium"
          >
            {isActive ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
            {isActive ? t('focus.pause') : t('focus.start')}
          </button>
          <button
            onClick={resetTimer}
            className="flex items-center gap-2 px-6 py-3 bg-accent text-foreground rounded-lg hover:bg-accent/80 transition-all duration-200 font-medium"
          >
            <Square className="w-5 h-5" />
            {t('focus.stop')}
          </button>
        </div>

        {/* Ambient Sounds */}
        <div className="space-y-4">
          <div className={cn(
            "flex items-center justify-between",
            isRTL && "flex-row-reverse"
          )}>
            <h3 className="font-semibold">{t('focus.ambientSounds')}</h3>
            <button
              onClick={() => setSoundEnabled(!soundEnabled)}
              className="p-2 hover:bg-accent rounded-lg transition-colors"
            >
              {soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
            </button>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            {ambientSounds.map((sound) => (
              <button
                key={sound.id}
                onClick={() => setSelectedSound(selectedSound === sound.id ? null : sound.id)}
                className={cn(
                  "flex items-center gap-2 p-3 rounded-lg transition-all duration-200 text-sm",
                  selectedSound === sound.id
                    ? "bg-zenith-gradient text-white shadow-zenith"
                    : "bg-accent hover:bg-accent/80",
                  isRTL && "flex-row-reverse"
                )}
              >
                <span className="text-lg">{sound.emoji}</span>
                <span>{sound.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FocusMode;
