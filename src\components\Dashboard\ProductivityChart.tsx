
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useLanguage } from '@/contexts/LanguageContext';
import { useColors } from '@/hooks/useColors';

const ProductivityChart: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { chart } = useColors();

  const data = [
    { name: isRTL ? 'السبت' : 'Sat', tasks: 4, hours: 6 },
    { name: isRTL ? 'الأحد' : 'Sun', tasks: 6, hours: 8 },
    { name: isRTL ? 'الاثنين' : 'Mon', tasks: 8, hours: 7 },
    { name: isRTL ? 'الثلاثاء' : 'Tue', tasks: 5, hours: 6 },
    { name: isRTL ? 'الأربعاء' : 'Wed', tasks: 9, hours: 9 },
    { name: isRT<PERSON> ? 'الخميس' : 'Thu', tasks: 7, hours: 8 },
    { name: isRT<PERSON> ? 'الجمعة' : 'Fri', tasks: 6, hours: 5 },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 transition-colors duration-200">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">{t('analytics.productivity')}</h2>
        <p className="text-gray-600 dark:text-gray-400 text-sm">{t('analytics.thisWeek')}</p>
      </div>

      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="name" 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'hsl(var(--card))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '8px',
              }}
            />
            <Bar
              dataKey="tasks"
              fill={chart.primary}
              radius={[4, 4, 0, 0]}
              name={t('tasks.title')}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ProductivityChart;
