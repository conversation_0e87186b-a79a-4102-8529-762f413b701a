
import React from 'react';
import { FileText, Plus, Search, Filter } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const NotesGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();

  const notes = [
    {
      id: '1',
      title: isRTL ? 'أفكار المشروع الجديد' : 'New Project Ideas',
      content: isRTL 
        ? 'قائمة بالأفكار المبتكرة للمشاريع القادمة في مجال التكنولوجيا...'
        : 'List of innovative ideas for upcoming technology projects...',
      lastModified: '2024-01-15',
      wordCount: 156,
      tags: ['أفكار', 'مشاريع'],
      color: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20',
    },
    {
      id: '2',
      title: isRTL ? 'ملاحظات الاجتماع' : 'Meeting Notes',
      content: isRTL
        ? 'نقاط مهمة من اجتماع الفريق حول التطوير الجديد...'
        : 'Important points from team meeting about new development...',
      lastModified: '2024-01-14',
      wordCount: 89,
      tags: ['اجتماع', 'فريق'],
      color: 'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20',
    },
    {
      id: '3',
      title: isRTL ? 'قائمة المهام الأسبوعية' : 'Weekly Task List',
      content: isRTL
        ? 'المهام المطلوب إنجازها خلال هذا الأسبوع مع الأولويات...'
        : 'Tasks to be completed this week with priorities...',
      lastModified: '2024-01-13',
      wordCount: 234,
      tags: ['مهام', 'أسبوعي'],
      color: 'from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20',
    },
  ];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('notes.title')}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">{t('notes.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('notes.newNote')}
        </button>
      </div>

      {/* Search and Filter */}
      <div className={cn(
        "flex items-center gap-4",
        isRTL && "flex-row-reverse"
      )}>
        <div className="flex-1 relative">
          <Search className={cn(
            "absolute top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5",
            isRTL ? "right-3" : "left-3"
          )} />
          <input
            type="text"
            placeholder={isRTL ? 'ابحث في الملاحظات...' : 'Search notes...'}
            className={cn(
              "w-full bg-card border border-border rounded-lg py-3 px-12 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
              isRTL ? "text-right pr-12" : "text-left pl-12"
            )}
          />
        </div>
        <button className="p-3 bg-card hover:bg-accent border border-border rounded-lg transition-colors">
          <Filter className="w-5 h-5" />
        </button>
      </div>

      {/* Notes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {notes.map((note) => (
          <div
            key={note.id}
            className={cn(
              `bg-gradient-to-br ${note.color} border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-lg hover:shadow-gray-200/50 dark:hover:shadow-gray-900/50 transition-all duration-200 cursor-pointer group`
            )}
          >
            <div className="mb-4">
              <h3 className={cn(
                "text-lg font-semibold mb-2 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",
                isRTL && "text-right"
              )}>
                {note.title}
              </h3>
              <p className={cn(
                "text-gray-600 dark:text-gray-400 text-sm line-clamp-3",
                isRTL && "text-right"
              )}>
                {note.content}
              </p>
            </div>

            <div className={cn(
              "flex items-center justify-between text-xs text-muted-foreground mb-3",
              isRTL && "flex-row-reverse"
            )}>
              <span>{t('notes.lastModified')}: {note.lastModified}</span>
              <span>{note.wordCount} {t('notes.wordCount')}</span>
            </div>

            <div className={cn(
              "flex gap-1 flex-wrap",
              isRTL && "flex-row-reverse"
            )}>
              {note.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-white/60 dark:bg-black/20 text-foreground rounded text-xs font-medium"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        ))}

        {/* Add New Note Card */}
        <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 cursor-pointer group">
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-zenith-gradient rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('notes.newNote')}</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {isRTL ? 'ابدأ في كتابة أفكارك' : 'Start writing your thoughts'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotesGrid;
