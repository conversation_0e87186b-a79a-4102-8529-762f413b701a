
import React from 'react';
import { Calendar, Users, TrendingUp, Plus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';

const ProjectsGrid: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { projectStatus } = useColors();

  const projects = [
    {
      id: '1',
      title: isRTL ? 'تطبيق الهاتف المحمول' : 'Mobile App Development',
      description: isRTL ? 'تطوير تطبيق جديد لنظامي iOS و Android' : 'Developing new app for iOS and Android',
      progress: 75,
      team: 5,
      tasks: 24,
      deadline: '2024-02-15',
      status: 'active',
      color: 'from-blue-500 to-blue-600',
    },
    {
      id: '2',
      title: isRTL ? 'إعادة تصميم الموقع' : 'Website Redesign',
      description: isRTL ? 'تحديث تصميم الموقع الرئيسي للشركة' : 'Updating main company website design',
      progress: 40,
      team: 3,
      tasks: 18,
      deadline: '2024-01-30',
      status: 'active',
      color: 'from-green-500 to-green-600',
    },
    {
      id: '3',
      title: isRTL ? 'نظام إدارة المحتوى' : 'Content Management System',
      description: isRTL ? 'بناء نظام CMS مخصص للمحتوى' : 'Building custom CMS for content management',
      progress: 90,
      team: 4,
      tasks: 32,
      deadline: '2024-01-20',
      status: 'completed',
      color: 'from-purple-500 to-purple-600',
    },
  ];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('projects.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('projects.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('projects.newProject')}
        </button>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project) => (
          <div
            key={project.id}
            className="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 group"
          >
            {/* Project Header */}
            <div className="mb-4">
              <div className={cn(
                "flex items-start justify-between mb-3",
                isRTL && "flex-row-reverse"
              )}>
                <div className={cn(
                  `w-3 h-3 rounded-full bg-gradient-to-r ${project.color}`
                )}></div>
                <span className={cn(
                  "text-xs px-2 py-1 rounded-full font-medium",
                  project.status === 'active'
                    ? projectStatus.active.className
                    : project.status === 'completed'
                    ? projectStatus.completed.className
                    : projectStatus.pending.className
                )}>
                  {t(`projects.status.${project.status}`)}
                </span>
              </div>
              
              <h3 className={cn(
                "text-lg font-semibold mb-2 group-hover:text-primary transition-colors",
                isRTL && "text-right"
              )}>
                {project.title}
              </h3>
              
              <p className={cn(
                "text-muted-foreground text-sm",
                isRTL && "text-right"
              )}>
                {project.description}
              </p>
            </div>

            {/* Progress */}
            <div className="mb-4">
              <div className={cn(
                "flex items-center justify-between text-sm mb-2",
                isRTL && "flex-row-reverse"
              )}>
                <span className="text-muted-foreground">{t('projects.progress')}</span>
                <span className="font-medium">{project.progress}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={cn(
                    `h-full rounded-full bg-gradient-to-r ${project.color} transition-all duration-500`
                  )}
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>
            </div>

            {/* Stats */}
            <div className={cn(
              "flex items-center justify-between text-sm text-muted-foreground",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn(
                "flex items-center gap-1",
                isRTL && "flex-row-reverse"
              )}>
                <Users className="w-4 h-4" />
                <span>{project.team} {t('projects.members')}</span>
              </div>
              
              <div className={cn(
                "flex items-center gap-1",
                isRTL && "flex-row-reverse"
              )}>
                <TrendingUp className="w-4 h-4" />
                <span>{project.tasks} {t('projects.tasks')}</span>
              </div>
              
              <div className={cn(
                "flex items-center gap-1",
                isRTL && "flex-row-reverse"
              )}>
                <Calendar className="w-4 h-4" />
                <span>{project.deadline}</span>
              </div>
            </div>
          </div>
        ))}

        {/* Add New Project Card */}
        <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-6 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 cursor-pointer group">
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-zenith-gradient rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200">
              <Plus className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{t('projects.createNew')}</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">{t('projects.startOrganizing')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectsGrid;
